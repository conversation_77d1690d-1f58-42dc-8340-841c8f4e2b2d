// ==UserScript==
// @name         名扬自动填写SKU
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动填写zzc168.com订单中心的SKU信息
// <AUTHOR>
// @match        https://www.zzc168.com/*
// @match        http://www.zzc168.com/*
// @run-at       document-end
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 名扬自动填写SKU - 油猴脚本版本
    class AutoFillSKU {
        constructor() {
            this.isRunning = false;
            this.floatingButton = null;
            this.init();
        }

        init() {
            // 延迟创建按钮，确保页面完全加载
            setTimeout(() => {
                this.createFloatingButton();
                console.log('名扬自动填写SKU已加载');
            }, 2000);
        }

        // 创建浮动按钮
        createFloatingButton() {
            this.floatingButton = document.createElement('div');
            this.floatingButton.innerHTML = '自动填写';
            this.floatingButton.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                width: 80px;
                height: 80px;
                background-color: #1890ff;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10000;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            this.floatingButton.addEventListener('click', () => {
                this.executeAutoFill();
            });

            this.floatingButton.addEventListener('mouseenter', () => {
                this.floatingButton.style.transform = 'scale(1.1)';
            });

            this.floatingButton.addEventListener('mouseleave', () => {
                this.floatingButton.style.transform = 'scale(1)';
            });

            document.body.appendChild(this.floatingButton);
        }

        // 执行自动填写
        async executeAutoFill() {
            if (this.isRunning) {
                console.log('自动填写正在运行中...');
                return;
            }

            this.isRunning = true;
            this.floatingButton.innerHTML = '运行中...';
            this.floatingButton.style.backgroundColor = '#ff6b6b';

            try {
                // 查找关联货源弹窗
                const modal = document.querySelector('.ant-modal-content');
                if (!modal) {
                    alert('请先打开关联货源弹窗');
                    return;
                }

                const title = modal.querySelector('.ant-modal-title');
                if (!title || !title.textContent.includes('关联货源')) {
                    alert('请确保关联货源弹窗已打开');
                    return;
                }

                console.log('开始自动填写货源编号...');

                // 填写货源编号
                await this.fillSourceNumbers();

                console.log('✅ 自动填写完成！');
                alert('货源编号填写完成！');

            } catch (error) {
                console.error('自动填写失败:', error);
                alert('自动填写失败: ' + error.message);
            } finally {
                this.isRunning = false;
                this.floatingButton.innerHTML = '自动填写';
                this.floatingButton.style.backgroundColor = '#1890ff';
            }
        }

        // 填写货源编号 - 使用最有效的方法
        async fillSourceNumbers() {
            const modal = document.querySelector('.ant-modal-content');
            const rows = modal.querySelectorAll('.sku-table .ant-table-tbody tr:not(.ant-table-measure-row)');
            
            console.log(`找到 ${rows.length} 行SKU数据`);

            for (let i = 0; i < rows.length; i++) {
                const select = rows[i].querySelectorAll('td')[2].querySelector('.ant-select');
                const placeholder = select?.querySelector('.ant-select-selection-placeholder');
                
                if (placeholder && placeholder.textContent.includes('请选择编号')) {
                    console.log(`填写第 ${i + 1} 行货源编号...`);
                    
                    // 使用最有效的点击方法
                    const success = await this.clickSelectAndChooseOption(select);
                    if (success) {
                        console.log(`✅ 第 ${i + 1} 行货源编号填写成功`);
                    } else {
                        console.log(`❌ 第 ${i + 1} 行货源编号填写失败`);
                    }
                    
                    await this.sleep(500);
                }
            }
        }

        // 改进的选择框点击和选择方法
        async clickSelectAndChooseOption(select) {
            try {
                // 关闭其他可能打开的下拉框
                const modal = document.querySelector('.ant-modal-content');
                modal.click();
                await this.sleep(200);
                
                // 获取选择器元素
                const selector = select.querySelector('.ant-select-selector');
                if (!selector) return false;
                
                // 模拟真实的用户点击序列
                selector.focus();
                await this.sleep(100);
                
                // 触发完整的鼠标事件序列
                const events = ['mousedown', 'mouseup', 'click'];
                for (const eventType of events) {
                    const event = new MouseEvent(eventType, {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        button: 0
                    });
                    selector.dispatchEvent(event);
                    await this.sleep(50);
                }
                
                // 等待下拉框出现
                await this.sleep(1000);
                
                // 查找并点击选项
                const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                if (!dropdown) return false;
                
                // 尝试多种方式找到选项
                let option = null;
                
                // 方式1: 虚拟列表
                const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
                if (virtualList) {
                    const options = virtualList.querySelectorAll('.ant-select-item');
                    if (options.length > 0) {
                        option = options[0];
                    }
                }
                
                // 方式2: 普通选项
                if (!option) {
                    const options = dropdown.querySelectorAll('.ant-select-item');
                    if (options.length > 0) {
                        option = options[0];
                    }
                }
                
                if (!option) return false;
                
                console.log(`找到选项: ${option.textContent.trim()}`);
                
                // 尝试多种点击方式确保成功
                
                // 方式1: 直接点击
                option.click();
                await this.sleep(100);
                
                // 方式2: 触发鼠标事件
                const mouseenterEvent = new MouseEvent('mouseenter', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                option.dispatchEvent(mouseenterEvent);
                await this.sleep(50);
                
                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    button: 0
                });
                option.dispatchEvent(clickEvent);
                await this.sleep(100);
                
                // 方式3: 键盘事件
                const keyEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    bubbles: true,
                    cancelable: true
                });
                option.dispatchEvent(keyEvent);
                
                return true;
            } catch (error) {
                console.error('点击选择框失败:', error);
                return false;
            }
        }

        // 延时函数
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // 页面加载完成后初始化
    console.log('油猴脚本开始加载...');

    // 多种方式确保脚本正确加载
    function initScript() {
        console.log('正在初始化名扬自动填写SKU...');
        try {
            new AutoFillSKU();
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    // 方式1: 立即执行
    initScript();

    // 方式2: DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScript);
    }

    // 方式3: 页面完全加载后执行
    window.addEventListener('load', initScript);

    // 方式4: 延迟执行（适用于SPA应用）
    setTimeout(initScript, 3000);

})();
