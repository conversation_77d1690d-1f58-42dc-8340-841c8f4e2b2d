// ==UserScript==
// @name         名扬自动填写SKU
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动填写zzc168.com订单中心的SKU信息
// <AUTHOR>
// @match        https://www.zzc168.com/*
// @match        http://www.zzc168.com/*
// @run-at       document-end
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 名扬自动填写SKU - 油猴脚本版本
    class AutoFillSKU {
        constructor() {
            this.isRunning = false;
            this.floatingButton = null;
            this.init();
        }

        init() {
            // 延迟创建按钮，确保页面完全加载
            setTimeout(() => {
                this.createFloatingButton();
                console.log('名扬自动填写SKU已加载');
            }, 2000);
        }

        // 创建浮动按钮
        createFloatingButton() {
            this.floatingButton = document.createElement('div');
            this.floatingButton.innerHTML = '自动填写';
            this.floatingButton.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                width: 80px;
                height: 80px;
                background-color: #1890ff;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10000;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            this.floatingButton.addEventListener('click', () => {
                this.executeAutoFill();
            });

            this.floatingButton.addEventListener('mouseenter', () => {
                this.floatingButton.style.transform = 'scale(1.1)';
            });

            this.floatingButton.addEventListener('mouseleave', () => {
                this.floatingButton.style.transform = 'scale(1)';
            });

            document.body.appendChild(this.floatingButton);
        }

        // 执行自动填写
        async executeAutoFill() {
            if (this.isRunning) {
                console.log('自动填写正在运行中...');
                return;
            }

            this.isRunning = true;
            this.floatingButton.innerHTML = '运行中...';
            this.floatingButton.style.backgroundColor = '#ff6b6b';

            try {
                // 查找关联货源弹窗
                const modal = document.querySelector('.ant-modal-content');
                if (!modal) {
                    alert('请先打开关联货源弹窗');
                    return;
                }

                const title = modal.querySelector('.ant-modal-title');
                if (!title || !title.textContent.includes('关联货源')) {
                    alert('请确保关联货源弹窗已打开');
                    return;
                }

                console.log('开始自动填写货源编号...');

                // 填写货源编号
                await this.fillSourceNumbers();

                console.log('✅ 自动填写完成！');
                alert('货源编号填写完成！');

            } catch (error) {
                console.error('自动填写失败:', error);
                alert('自动填写失败: ' + error.message);
            } finally {
                this.isRunning = false;
                this.floatingButton.innerHTML = '自动填写';
                this.floatingButton.style.backgroundColor = '#1890ff';
            }
        }

        // 完整的自动填写流程
        async fillSourceNumbers() {
            const modal = document.querySelector('.ant-modal-content');
            const rows = modal.querySelectorAll('.sku-table .ant-table-tbody tr:not(.ant-table-measure-row)');

            console.log(`🚀 开始完整填写流程，共 ${rows.length} 行SKU数据`);

            // 步骤1: 逐行填写货源编号
            console.log('\n=== 步骤1: 填写所有货源编号 ===');
            for (let i = 0; i < rows.length; i++) {
                const select = rows[i].querySelectorAll('td')[2].querySelector('.ant-select');
                const placeholder = select?.querySelector('.ant-select-selection-placeholder');

                if (placeholder && placeholder.textContent.includes('请选择编号')) {
                    console.log(`⚡ 填写第 ${i + 1} 行货源编号...`);

                    const success = await this.clickSelectAndChooseOption(select);
                    if (success) {
                        console.log(`✅ 第 ${i + 1} 行货源编号完成`);
                        // 等待货源SKU选项加载
                        await this.sleep(800);
                    } else {
                        console.log(`❌ 第 ${i + 1} 行货源编号失败`);
                    }
                }
            }

            // 步骤2: 等待所有货源SKU选项加载
            console.log('\n=== 步骤2: 等待货源SKU选项加载 ===');
            await this.sleep(2000);

            // 步骤3: 智能匹配货源SKU
            console.log('\n=== 步骤3: 智能匹配货源SKU ===');
            await this.intelligentSKUMatching();
        }

        // 改进的选择框点击和选择方法 - 修复下拉框关闭问题
        async clickSelectAndChooseOption(select) {
            try {
                console.log('🔍 开始调试选择框点击过程...');

                // 强力关闭所有下拉框
                await this.forceCloseAllDropdowns();
                await this.sleep(300);

                // 获取选择器元素
                const selector = select.querySelector('.ant-select-selector');
                if (!selector) {
                    console.log('❌ 未找到选择器元素');
                    return false;
                }

                console.log('✅ 找到选择器元素');

                // 模拟真实的用户点击序列
                selector.focus();
                await this.sleep(100);

                // 触发完整的鼠标事件序列
                const events = ['mousedown', 'mouseup', 'click'];
                for (const eventType of events) {
                    const event = new MouseEvent(eventType, {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        button: 0
                    });
                    selector.dispatchEvent(event);
                    await this.sleep(50);
                }

                console.log('✅ 已触发点击事件');

                // 等待下拉框出现
                await this.sleep(1000);

                // 调试：检查所有打开的下拉框
                const allDropdowns = document.querySelectorAll('.ant-select-dropdown');
                const openDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');

                console.log(`📊 调试信息:`);
                console.log(`- 总下拉框数量: ${allDropdowns.length}`);
                console.log(`- 打开的下拉框数量: ${openDropdowns.length}`);

                // 详细分析每个打开的下拉框
                openDropdowns.forEach((dropdown, index) => {
                    console.log(`\n📋 下拉框 ${index + 1}:`);
                    console.log(`- 位置: left=${dropdown.style.left}, top=${dropdown.style.top}`);
                    console.log(`- 类名: ${dropdown.className}`);

                    // 检查虚拟列表
                    const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
                    if (virtualList) {
                        const options = virtualList.querySelectorAll('.ant-select-item');
                        console.log(`- 虚拟列表选项数量: ${options.length}`);
                        options.forEach((option, optIndex) => {
                            const content = option.querySelector('.ant-select-item-option-content');
                            const text = content ? content.textContent.trim() : option.textContent.trim();
                            console.log(`  选项 ${optIndex + 1}: "${text}"`);
                        });
                    } else {
                        // 检查普通选项
                        const options = dropdown.querySelectorAll('.ant-select-item');
                        console.log(`- 普通选项数量: ${options.length}`);
                        options.forEach((option, optIndex) => {
                            console.log(`  选项 ${optIndex + 1}: "${option.textContent.trim()}"`);
                        });
                    }
                });

                // 查找当前可见的下拉框（排除负坐标的隐藏下拉框）
                let targetOption = null;

                for (let dropdown of openDropdowns) {
                    // 检查下拉框是否在可见位置
                    const left = parseInt(dropdown.style.left) || 0;
                    const top = parseInt(dropdown.style.top) || 0;

                    console.log(`检查下拉框位置: left=${left}, top=${top}`);

                    // 只处理可见位置的下拉框（坐标为正数）
                    if (left > 0 && top > 0) {
                        console.log(`✅ 找到可见下拉框`);

                        const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
                        if (virtualList) {
                            const options = virtualList.querySelectorAll('.ant-select-item');
                            for (let option of options) {
                                const content = option.querySelector('.ant-select-item-option-content');
                                const text = content ? content.textContent.trim() : option.textContent.trim();
                                if (text === '编号1' || text.includes('编号1')) {
                                    console.log(`🎯 在可见下拉框中找到编号1选项！`);
                                    targetOption = option;
                                    break;
                                }
                            }
                        } else {
                            const options = dropdown.querySelectorAll('.ant-select-item');
                            for (let option of options) {
                                const text = option.textContent.trim();
                                if (text === '编号1' || text.includes('编号1')) {
                                    console.log(`🎯 在可见下拉框中找到编号1选项！`);
                                    targetOption = option;
                                    break;
                                }
                            }
                        }

                        if (targetOption) break;
                    } else {
                        console.log(`❌ 跳过隐藏的下拉框 (left=${left}, top=${top})`);
                    }
                }

                if (!targetOption) {
                    console.log('❌ 在可见下拉框中未找到编号1选项');
                    return false;
                }

                if (!targetOption) {
                    console.log('❌ 完全没有找到任何选项');
                    return false;
                }

                console.log(`🎯 准备点击选项: ${targetOption.textContent.trim()}`);

                // 尝试多种点击方式确保成功

                // 方式1: 直接点击
                targetOption.click();
                await this.sleep(100);

                // 方式2: 触发鼠标事件
                const mouseenterEvent = new MouseEvent('mouseenter', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                targetOption.dispatchEvent(mouseenterEvent);
                await this.sleep(50);

                const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    button: 0
                });
                targetOption.dispatchEvent(clickEvent);
                await this.sleep(100);

                // 方式3: 键盘事件
                const keyEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    bubbles: true,
                    cancelable: true
                });
                targetOption.dispatchEvent(keyEvent);

                console.log('✅ 已尝试所有点击方式');

                return true;
            } catch (error) {
                console.error('❌ 点击选择框失败:', error);
                return false;
            }
        }

        // 强力关闭所有下拉框
        async forceCloseAllDropdowns() {
            console.log('🔧 强力关闭所有下拉框...');

            // 方法1: 点击模态框背景
            const modal = document.querySelector('.ant-modal-content');
            if (modal) {
                modal.click();
                await this.sleep(100);
            }

            // 方法2: 按ESC键
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27,
                bubbles: true,
                cancelable: true
            }));
            await this.sleep(100);

            // 方法3: 直接移除所有下拉框元素
            const allDropdowns = document.querySelectorAll('.ant-select-dropdown');
            allDropdowns.forEach(dropdown => {
                if (!dropdown.classList.contains('ant-select-dropdown-hidden')) {
                    dropdown.classList.add('ant-select-dropdown-hidden');
                    dropdown.style.display = 'none';
                }
            });

            console.log(`🔧 已处理 ${allDropdowns.length} 个下拉框`);
        }

        // 智能SKU匹配主函数
        async intelligentSKUMatching() {
            const modal = document.querySelector('.ant-modal-content');
            const rows = modal.querySelectorAll('.sku-table .ant-table-tbody tr:not(.ant-table-measure-row)');

            console.log(`🧠 开始智能SKU匹配，共 ${rows.length} 行`);

            // 收集所有商品SKU信息
            const productSKUs = [];
            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].querySelectorAll('td');
                const productSKUText = cells[0]?.textContent?.trim(); // 第1列：商品SKU
                const sourceSKUSelect = cells[4]?.querySelector('.ant-select'); // 第5列：货源SKU

                if (productSKUText && sourceSKUSelect) {
                    productSKUs.push({
                        index: i,
                        productSKU: productSKUText,
                        sourceSKUSelect: sourceSKUSelect,
                        row: rows[i],
                        matched: false
                    });
                }
            }

            console.log(`📋 收集到 ${productSKUs.length} 个商品SKU:`);
            productSKUs.forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.productSKU}`);
            });

            // 按字数排序（字数多的优先匹配）
            productSKUs.sort((a, b) => b.productSKU.length - a.productSKU.length);
            console.log('\n📊 按字数排序后的匹配顺序:');
            productSKUs.forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.productSKU} (${item.productSKU.length}字)`);
            });

            // 已使用的货源SKU选项
            const usedSourceSKUs = new Set();

            // 逐个匹配
            for (let i = 0; i < productSKUs.length; i++) {
                const item = productSKUs[i];
                console.log(`\n🎯 匹配第 ${i + 1} 个: ${item.productSKU}`);

                const success = await this.matchSingleSKU(item, usedSourceSKUs);
                if (success) {
                    item.matched = true;
                    console.log(`✅ 匹配成功`);
                } else {
                    console.log(`❌ 匹配失败`);
                }

                await this.sleep(300);
            }

            // 匹配结果统计
            const matchedCount = productSKUs.filter(item => item.matched).length;
            console.log(`\n📊 匹配结果: ${matchedCount}/${productSKUs.length} 成功`);
        }

        // 匹配单个SKU
        async matchSingleSKU(item, usedSourceSKUs) {
            try {
                // 检查是否已经填写
                const placeholder = item.sourceSKUSelect.querySelector('.ant-select-selection-placeholder');
                const selectedItem = item.sourceSKUSelect.querySelector('.ant-select-selection-item');

                if (selectedItem && !selectedItem.textContent.includes('请选择sku')) {
                    console.log(`  已填写: ${selectedItem.textContent.trim()}`);
                    return true;
                }

                if (!placeholder || !placeholder.textContent.includes('请选择sku')) {
                    console.log(`  状态异常，跳过`);
                    return false;
                }

                // 点击打开下拉框
                console.log(`  点击打开货源SKU下拉框...`);
                item.sourceSKUSelect.click();
                await this.sleep(800);

                // 获取所有可用选项
                const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                if (!dropdown) {
                    console.log(`  未找到下拉框`);
                    return false;
                }

                const options = this.getDropdownOptions(dropdown);
                console.log(`  发现 ${options.length} 个货源SKU选项`);

                // 过滤未使用的选项
                const availableOptions = options.filter(opt => !usedSourceSKUs.has(opt.text));
                console.log(`  其中 ${availableOptions.length} 个未使用`);

                if (availableOptions.length === 0) {
                    console.log(`  没有可用选项`);
                    // 关闭下拉框
                    document.querySelector('.ant-modal-content').click();
                    return false;
                }

                // 智能匹配最佳选项
                const bestMatch = this.findBestMatch(item.productSKU, availableOptions);

                if (bestMatch) {
                    console.log(`  最佳匹配: ${bestMatch.text} (匹配度: ${bestMatch.score})`);

                    // 点击选择
                    bestMatch.element.click();
                    usedSourceSKUs.add(bestMatch.text);

                    await this.sleep(300);
                    return true;
                } else {
                    console.log(`  未找到合适的匹配`);
                    // 关闭下拉框
                    document.querySelector('.ant-modal-content').click();
                    return false;
                }

            } catch (error) {
                console.error(`  匹配出错:`, error);
                return false;
            }
        }

        // 获取下拉框选项
        getDropdownOptions(dropdown) {
            const options = [];

            // 尝试虚拟列表
            const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
            if (virtualList) {
                const items = virtualList.querySelectorAll('.ant-select-item');
                items.forEach(item => {
                    const content = item.querySelector('.ant-select-item-option-content');
                    const text = content ? content.textContent.trim() : item.textContent.trim();
                    if (text && text !== '') {
                        options.push({ element: item, text: text });
                    }
                });
            } else {
                // 普通列表
                const items = dropdown.querySelectorAll('.ant-select-item');
                items.forEach(item => {
                    const text = item.textContent.trim();
                    if (text && text !== '') {
                        options.push({ element: item, text: text });
                    }
                });
            }

            return options;
        }

        // 智能匹配算法
        findBestMatch(productSKU, availableOptions) {
            console.log(`    🔍 匹配商品SKU: ${productSKU}`);

            // 提取商品SKU的关键信息
            const productInfo = this.extractSKUInfo(productSKU);
            console.log(`    📝 商品信息:`, productInfo);

            let bestMatch = null;
            let bestScore = 0;

            // 为每个选项计算匹配度
            availableOptions.forEach((option, index) => {
                const sourceInfo = this.extractSKUInfo(option.text);
                const score = this.calculateMatchScore(productInfo, sourceInfo);

                console.log(`    ${index + 1}. ${option.text} -> 匹配度: ${score}`);

                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = { ...option, score };
                }
            });

            // 只有匹配度超过阈值才返回
            if (bestScore >= 0.6) {
                return bestMatch;
            }

            return null;
        }

        // 提取SKU关键信息
        extractSKUInfo(skuText) {
            // 尺码匹配规则
            const sizePattern = /\b(80|90|100|110|120|130|140|150|160|170|S|M|L|XL|2XL|3XL)\b/gi;
            const sizes = [...skuText.matchAll(sizePattern)].map(match => match[0].toUpperCase());

            // 判断是否为套装
            const isSet = /套装|两件套|三件套|四件套|一套|\+|,|，/.test(skuText);

            // 提取关键词（去除尺码、标点符号等）
            let keywords = skuText
                .replace(/\d+(cm|CM)?/g, '') // 移除尺码
                .replace(/[;；,，+\-\s]+/g, ' ') // 替换分隔符为空格
                .replace(/套装|两件套|三件套|四件套|一套/g, '') // 移除套装标识
                .trim()
                .split(/\s+/)
                .filter(word => word.length > 0);

            // 移除常见无意义词汇
            const stopWords = ['的', '和', '与', '及', '或'];
            keywords = keywords.filter(word => !stopWords.includes(word));

            return {
                original: skuText,
                sizes: sizes,
                isSet: isSet,
                keywords: keywords,
                keywordCount: keywords.length
            };
        }

        // 计算匹配度
        calculateMatchScore(productInfo, sourceInfo) {
            let score = 0;

            // 1. 尺码匹配（权重40%）- 必须完全匹配
            if (productInfo.sizes.length > 0 && sourceInfo.sizes.length > 0) {
                const sizeMatch = productInfo.sizes.some(size =>
                    sourceInfo.sizes.some(sourceSize =>
                        size.toUpperCase() === sourceSize.toUpperCase()
                    )
                );
                if (sizeMatch) {
                    score += 0.4;
                } else {
                    // 尺码不匹配直接返回0
                    return 0;
                }
            } else if (productInfo.sizes.length === 0 && sourceInfo.sizes.length === 0) {
                // 都没有尺码也算匹配
                score += 0.4;
            } else {
                // 一个有尺码一个没有，不匹配
                return 0;
            }

            // 2. 套装类型匹配（权重20%）
            if (productInfo.isSet === sourceInfo.isSet) {
                score += 0.2;
            } else if (productInfo.isSet && !sourceInfo.isSet) {
                // 商品是套装但货源不是，降低匹配度
                score -= 0.1;
            }

            // 3. 关键词匹配（权重40%）
            if (productInfo.keywords.length > 0 && sourceInfo.keywords.length > 0) {
                let keywordScore = 0;
                let totalKeywords = productInfo.keywords.length;

                productInfo.keywords.forEach(productKeyword => {
                    // 完全匹配
                    if (sourceInfo.keywords.some(sourceKeyword =>
                        sourceKeyword.includes(productKeyword) || productKeyword.includes(sourceKeyword)
                    )) {
                        keywordScore += 1;
                    }
                    // 部分匹配（至少2个字符）
                    else if (productKeyword.length >= 2) {
                        sourceInfo.keywords.forEach(sourceKeyword => {
                            if (sourceKeyword.length >= 2) {
                                const commonChars = this.getCommonCharacters(productKeyword, sourceKeyword);
                                if (commonChars >= 2) {
                                    keywordScore += 0.5;
                                }
                            }
                        });
                    }
                });

                score += (keywordScore / totalKeywords) * 0.4;
            }

            return Math.min(score, 1.0); // 最大值为1
        }

        // 获取两个字符串的公共字符数
        getCommonCharacters(str1, str2) {
            let common = 0;
            const chars1 = str1.split('');
            const chars2 = str2.split('');

            chars1.forEach(char => {
                const index = chars2.indexOf(char);
                if (index !== -1) {
                    common++;
                    chars2.splice(index, 1); // 避免重复计算
                }
            });

            return common;
        }

        // 延时函数
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // 页面加载完成后初始化
    console.log('油猴脚本开始加载...');

    // 多种方式确保脚本正确加载
    function initScript() {
        console.log('正在初始化名扬自动填写SKU...');
        try {
            new AutoFillSKU();
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    // 方式1: 立即执行
    initScript();

    // 方式2: DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScript);
    }

    // 方式3: 页面完全加载后执行
    window.addEventListener('load', initScript);

    // 方式4: 延迟执行（适用于SPA应用）
    setTimeout(initScript, 3000);

})();
