// ==UserScript==
// @name         名扬自动填写SKU
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动填写zzc168.com订单中心的SKU信息
// <AUTHOR>
// @match        https://www.zzc168.com/*
// @match        http://www.zzc168.com/*
// @run-at       document-end
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 名扬自动填写SKU - 油猴脚本版本
    class AutoFillSKU {
        constructor() {
            this.isRunning = false;
            this.floatingButton = null;
            this.init();
        }

        init() {
            // 延迟创建按钮，确保页面完全加载
            setTimeout(() => {
                this.createFloatingButton();
                console.log('名扬自动填写SKU已加载');
            }, 2000);
        }

        // 创建浮动按钮
        createFloatingButton() {
            this.floatingButton = document.createElement('div');
            this.floatingButton.innerHTML = '自动填写';
            this.floatingButton.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                width: 80px;
                height: 80px;
                background-color: #1890ff;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10000;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            this.floatingButton.addEventListener('click', () => {
                this.executeAutoFill();
            });

            this.floatingButton.addEventListener('mouseenter', () => {
                this.floatingButton.style.transform = 'scale(1.1)';
            });

            this.floatingButton.addEventListener('mouseleave', () => {
                this.floatingButton.style.transform = 'scale(1)';
            });

            document.body.appendChild(this.floatingButton);
        }

        // 执行自动填写
        async executeAutoFill() {
            if (this.isRunning) {
                console.log('自动填写正在运行中...');
                return;
            }

            this.isRunning = true;
            this.floatingButton.innerHTML = '运行中...';
            this.floatingButton.style.backgroundColor = '#ff6b6b';

            try {
                // 查找关联货源弹窗
                const modal = document.querySelector('.ant-modal-content');
                if (!modal) {
                    alert('请先打开关联货源弹窗');
                    return;
                }

                const title = modal.querySelector('.ant-modal-title');
                if (!title || !title.textContent.includes('关联货源')) {
                    alert('请确保关联货源弹窗已打开');
                    return;
                }

                console.log('开始自动填写货源编号...');

                // 填写货源编号
                await this.fillSourceNumbers();

                console.log('✅ 自动填写完成！');
                alert('货源编号填写完成！');

            } catch (error) {
                console.error('自动填写失败:', error);
                alert('自动填写失败: ' + error.message);
            } finally {
                this.isRunning = false;
                this.floatingButton.innerHTML = '自动填写';
                this.floatingButton.style.backgroundColor = '#1890ff';
            }
        }

        // 填写货源编号 - 超高速并行版本
        async fillSourceNumbers() {
            const modal = document.querySelector('.ant-modal-content');
            const rows = modal.querySelectorAll('.sku-table .ant-table-tbody tr:not(.ant-table-measure-row)');

            console.log(`🚀 超高速并行填写 ${rows.length} 行SKU数据`);

            // 收集所有需要填写的下拉框
            const selectsToFill = [];
            for (let i = 0; i < rows.length; i++) {
                const select = rows[i].querySelectorAll('td')[2].querySelector('.ant-select');
                const placeholder = select?.querySelector('.ant-select-selection-placeholder');

                if (placeholder && placeholder.textContent.includes('请选择编号')) {
                    selectsToFill.push({ index: i + 1, select });
                }
            }

            if (selectsToFill.length === 0) {
                console.log('没有需要填写的货源编号');
                return;
            }

            console.log(`找到 ${selectsToFill.length} 个需要填写的下拉框`);

            // 方法1: 同时打开所有下拉框
            await this.openAllDropdowns(selectsToFill);

            // 方法2: 等待所有下拉框加载完成（增加等待时间）
            console.log('⏳ 等待所有下拉框加载选项...');
            await this.sleep(1500);

            // 方法3: 检查下拉框状态
            await this.checkDropdownStatus();

            // 方法4: 同时选择所有"编号1"选项
            await this.selectAllOptions();

            console.log('✅ 并行填写完成！');
        }

        // 高速选择框点击方法
        async fastClickSelectAndChooseOption(select) {
            try {
                // 快速关闭所有下拉框
                await this.fastCloseAllDropdowns();
                await this.sleep(50);

                // 获取选择器元素
                const selector = select.querySelector('.ant-select-selector');
                if (!selector) return false;

                // 快速点击序列
                selector.focus();
                selector.click();

                // 短暂等待下拉框出现
                await this.sleep(200);

                // 快速查找下拉框
                const openDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');

                // 查找当前可见的下拉框（排除负坐标的隐藏下拉框）
                let targetOption = null;

                for (let dropdown of openDropdowns) {
                    // 快速检查下拉框是否在可见位置
                    const left = parseInt(dropdown.style.left) || 0;
                    const top = parseInt(dropdown.style.top) || 0;

                    // 只处理可见位置的下拉框
                    if (left > 0 && top > 0) {
                        const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
                        if (virtualList) {
                            const options = virtualList.querySelectorAll('.ant-select-item');
                            for (let option of options) {
                                const content = option.querySelector('.ant-select-item-option-content');
                                const text = content ? content.textContent.trim() : option.textContent.trim();
                                if (text === '编号1') {
                                    targetOption = option;
                                    break;
                                }
                            }
                        } else {
                            const options = dropdown.querySelectorAll('.ant-select-item');
                            for (let option of options) {
                                if (option.textContent.trim() === '编号1') {
                                    targetOption = option;
                                    break;
                                }
                            }
                        }
                        if (targetOption) break;
                    }
                }

                if (!targetOption) return false;

                // 快速点击选项
                targetOption.click();

                return true;
            } catch (error) {
                return false;
            }
        }

        // 同时打开所有下拉框
        async openAllDropdowns(selectsToFill) {
            console.log('🔥 同时打开所有下拉框...');

            // 先清理所有现有下拉框
            await this.fastCloseAllDropdowns();
            await this.sleep(200);

            // 分批打开下拉框，避免浏览器处理不过来
            const batchSize = 6; // 每批6个
            for (let i = 0; i < selectsToFill.length; i += batchSize) {
                const batch = selectsToFill.slice(i, i + batchSize);

                console.log(`⚡ 打开第 ${i + 1}-${Math.min(i + batchSize, selectsToFill.length)} 行下拉框`);

                // 同时点击这一批的下拉框
                batch.forEach(({ index, select }) => {
                    const selector = select.querySelector('.ant-select-selector');
                    if (selector) {
                        // 使用更强的事件触发
                        selector.focus();

                        // 触发mousedown和click事件
                        const mousedownEvent = new MouseEvent('mousedown', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            button: 0
                        });
                        selector.dispatchEvent(mousedownEvent);

                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            button: 0
                        });
                        selector.dispatchEvent(clickEvent);
                    }
                });

                // 每批之间稍微等待
                if (i + batchSize < selectsToFill.length) {
                    await this.sleep(100);
                }
            }
        }

        // 检查下拉框状态
        async checkDropdownStatus() {
            const allDropdowns = document.querySelectorAll('.ant-select-dropdown');
            const openDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');

            console.log(`📊 下拉框状态检查:`);
            console.log(`- 总下拉框数量: ${allDropdowns.length}`);
            console.log(`- 打开的下拉框数量: ${openDropdowns.length}`);

            // 如果没有打开的下拉框，尝试重新触发
            if (openDropdowns.length === 0) {
                console.log('⚠️ 没有检测到打开的下拉框，可能需要更多时间加载');
                console.log('🔄 再等待1秒...');
                await this.sleep(1000);

                const retryDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                console.log(`🔄 重新检查: 发现 ${retryDropdowns.length} 个打开的下拉框`);
            }
        }

        // 同时选择所有"编号1"选项
        async selectAllOptions() {
            console.log('🎯 同时选择所有编号1选项...');

            // 查找所有打开的下拉框
            const openDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
            console.log(`发现 ${openDropdowns.length} 个打开的下拉框`);

            let successCount = 0;

            // 同时处理所有下拉框
            openDropdowns.forEach((dropdown, index) => {
                // 检查是否为可见下拉框
                const left = parseInt(dropdown.style.left) || 0;
                const top = parseInt(dropdown.style.top) || 0;

                if (left > 0 && top > 0) {
                    // 查找"编号1"选项
                    const virtualList = dropdown.querySelector('.rc-virtual-list-holder-inner');
                    let option = null;

                    if (virtualList) {
                        const options = virtualList.querySelectorAll('.ant-select-item');
                        for (let opt of options) {
                            const content = opt.querySelector('.ant-select-item-option-content');
                            const text = content ? content.textContent.trim() : opt.textContent.trim();
                            if (text === '编号1') {
                                option = opt;
                                break;
                            }
                        }
                    } else {
                        const options = dropdown.querySelectorAll('.ant-select-item');
                        for (let opt of options) {
                            if (opt.textContent.trim() === '编号1') {
                                option = opt;
                                break;
                            }
                        }
                    }

                    if (option) {
                        console.log(`✅ 下拉框 ${index + 1} 选择编号1`);
                        option.click();
                        successCount++;
                    } else {
                        console.log(`❌ 下拉框 ${index + 1} 未找到编号1`);
                    }
                } else {
                    console.log(`⏭️ 跳过隐藏下拉框 ${index + 1}`);
                }
            });

            console.log(`🎉 成功选择了 ${successCount} 个编号1选项`);
        }

        // 快速关闭所有下拉框
        async fastCloseAllDropdowns() {
            // 直接隐藏所有下拉框
            const allDropdowns = document.querySelectorAll('.ant-select-dropdown');
            allDropdowns.forEach(dropdown => {
                if (!dropdown.classList.contains('ant-select-dropdown-hidden')) {
                    dropdown.classList.add('ant-select-dropdown-hidden');
                    dropdown.style.display = 'none';
                }
            });
        }

        // 强力关闭所有下拉框（保留原方法用于调试）
        async forceCloseAllDropdowns() {
            console.log('🔧 强力关闭所有下拉框...');

            // 方法1: 点击模态框背景
            const modal = document.querySelector('.ant-modal-content');
            if (modal) {
                modal.click();
                await this.sleep(100);
            }

            // 方法2: 按ESC键
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27,
                bubbles: true,
                cancelable: true
            }));
            await this.sleep(100);

            // 方法3: 直接移除所有下拉框元素
            const allDropdowns = document.querySelectorAll('.ant-select-dropdown');
            allDropdowns.forEach(dropdown => {
                if (!dropdown.classList.contains('ant-select-dropdown-hidden')) {
                    dropdown.classList.add('ant-select-dropdown-hidden');
                    dropdown.style.display = 'none';
                }
            });

            console.log(`🔧 已处理 ${allDropdowns.length} 个下拉框`);
        }

        // 延时函数
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // 页面加载完成后初始化
    console.log('油猴脚本开始加载...');

    // 多种方式确保脚本正确加载
    function initScript() {
        console.log('正在初始化名扬自动填写SKU...');
        try {
            new AutoFillSKU();
        } catch (error) {
            console.error('初始化失败:', error);
        }
    }

    // 方式1: 立即执行
    initScript();

    // 方式2: DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScript);
    }

    // 方式3: 页面完全加载后执行
    window.addEventListener('load', initScript);

    // 方式4: 延迟执行（适用于SPA应用）
    setTimeout(initScript, 3000);

})();
