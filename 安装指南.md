# 名扬自动填写SKU Chrome扩展 - 安装指南

## 第一步：生成图标文件

1. 在浏览器中打开 `create_icons.html` 文件
2. 页面会自动下载三个图标文件：
   - `icon16.ico`
   - `icon48.ico` 
   - `icon128.ico`
3. 将这些文件重命名为 `.png` 格式并放入 `icons/` 文件夹：
   - `icon16.png`
   - `icon48.png`
   - `icon128.png`

## 第二步：安装Chrome扩展

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含所有文件的项目文件夹
6. 扩展安装完成！

## 第三步：使用扩展

1. 打开网站：https://www.zzc168.com/#/orderCenter/order
2. 点击任意订单的"设置"按钮
3. 在弹出的"关联货源"弹窗中，点击页面右侧的蓝色"执行"按钮
4. 扩展将自动完成以下操作：
   - 填写所有货源编号为"编号1"
   - 智能匹配商品SKU与货源SKU

## 智能匹配说明

### 匹配规则
- **尺码优先**：尺码必须完全匹配（如90cm对应90）
- **字数优先**：字数多的商品SKU优先匹配
- **避免重复**：已匹配的货源SKU不会重复使用
- **关键词匹配**：基于关键词相似度进行智能匹配

### 匹配示例
```
商品SKU: "花边外套 条纹裤;140cm" 
↓ 匹配到
货源SKU: "花边外套+条纹裤 140"

商品SKU: "单花边外套;90cm"
↓ 匹配到  
货源SKU: "花边外套 90"
```

## 故障排除

### 扩展无法加载
- 确保所有文件都在同一个文件夹中
- 检查 `manifest.json` 文件格式是否正确
- 确保图标文件存在于 `icons/` 文件夹中

### 执行按钮不出现
- 确保在正确的网站页面：https://www.zzc168.com/#/orderCenter/order
- 刷新页面重试
- 检查浏览器控制台是否有错误信息

### 自动填写失败
- 确保"关联货源"弹窗已打开
- 检查网络连接是否正常
- 查看浏览器控制台的详细错误信息

## 文件清单

确保以下文件都存在：
- ✅ `manifest.json` - 扩展配置文件
- ✅ `content.js` - 主要功能脚本
- ✅ `content.css` - 样式文件
- ✅ `background.js` - 后台脚本
- ✅ `popup.html` - 扩展弹窗页面
- ✅ `popup.js` - 弹窗脚本
- ✅ `create_icons.html` - 图标生成工具
- ✅ `icons/icon16.png` - 16x16图标
- ✅ `icons/icon48.png` - 48x48图标
- ✅ `icons/icon128.png` - 128x128图标

## 技术支持

如果遇到问题，请：
1. 检查浏览器控制台的错误信息
2. 确认所有文件完整且格式正确
3. 尝试重新安装扩展
4. 检查Chrome版本是否支持（建议Chrome 88+）
