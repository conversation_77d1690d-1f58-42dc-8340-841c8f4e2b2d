// ==UserScript==
// @name         名扬自动填写SKU
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动填写zzc168.com订单中心的SKU信息
// <AUTHOR>
// @match        https://www.zzc168.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 名扬自动填写SKU - 油猴脚本版本
    class AutoFillSKU {
        constructor() {
            this.isRunning = false;
            this.floatingButton = null;
            this.usedSourceSKUs = new Set();
            this.init();
        }

    init() {
        // 检查是否在正确的页面
        if (window.location.href.includes('zzc168.com/#/orderCenter/order')) {
            this.createFloatingButton();
            this.observePageChanges();
        }
    }

    // 创建浮动按钮
    createFloatingButton() {
        if (this.floatingButton) return;

        this.floatingButton = document.createElement('div');
        this.floatingButton.id = 'auto-fill-sku-button';
        this.floatingButton.innerHTML = '执行';
        this.floatingButton.className = 'auto-fill-sku-floating-btn';
        
        // 添加点击事件
        this.floatingButton.addEventListener('click', () => {
            this.executeAutoFill();
        });

        // 添加右键调试功能
        this.floatingButton.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.debugMode();
        });

        document.body.appendChild(this.floatingButton);
    }

    // 监听页面变化
    observePageChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 检查是否有弹窗出现
                    const modal = document.querySelector('.ant-modal-content');
                    if (modal && !this.isRunning) {
                        // 延迟一下确保弹窗完全加载
                        setTimeout(() => {
                            this.checkModalContent(modal);
                        }, 500);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 检查弹窗内容
    checkModalContent(modal) {
        const title = modal.querySelector('.ant-modal-title');
        if (title && title.textContent.includes('关联货源')) {
            console.log('检测到关联货源弹窗');
        }
    }

    // 执行自动填写
    async executeAutoFill() {
        if (this.isRunning) {
            console.log('自动填写正在运行中...');
            return;
        }

        this.isRunning = true;
        this.floatingButton.innerHTML = '运行中...';
        this.floatingButton.style.backgroundColor = '#ff6b6b';

        try {
            // 查找关联货源弹窗
            const modal = document.querySelector('.ant-modal-content');
            if (!modal) {
                alert('请先打开关联货源弹窗');
                return;
            }

            const title = modal.querySelector('.ant-modal-title');
            if (!title || !title.textContent.includes('关联货源')) {
                alert('请确保关联货源弹窗已打开');
                return;
            }

            console.log('开始自动填写...');

            // 重置已使用的货源SKU记录
            this.usedSourceSKUs.clear();

            // 调试：分析页面结构
            this.debugPageStructure(modal);

            // 步骤0: 检查货源链接
            console.log('=== 步骤0: 检查货源链接 ===');
            await this.checkSourceLinks(modal);

            // 步骤1: 填写货源编号
            console.log('=== 步骤1: 填写货源编号 ===');
            await this.fillSourceNumbers(modal);

            // 步骤2: 等待SKU选项加载
            console.log('=== 步骤2: 等待SKU选项加载 ===');
            await this.waitForSKUOptions(modal);

            // 步骤3: 智能匹配SKU
            console.log('=== 步骤3: 智能匹配SKU ===');
            await this.matchSKUs(modal);

            console.log('自动填写完成');
            alert('自动填写完成！');

        } catch (error) {
            console.error('自动填写出错:', error);
            alert('自动填写出错: ' + error.message);
        } finally {
            this.isRunning = false;
            this.floatingButton.innerHTML = '执行';
            this.floatingButton.style.backgroundColor = '#4CAF50';
        }
    }

    // 检查货源链接
    async checkSourceLinks(modal) {
        console.log('检查货源链接状态...');

        // 查找货源链接表格
        const linkTable = modal.querySelector('.shopTable .ant-table-tbody');
        if (linkTable) {
            const rows = linkTable.querySelectorAll('tr:not(.ant-table-measure-row)');
            console.log(`找到 ${rows.length} 个货源链接行`);

            if (rows.length === 0) {
                console.log('⚠️ 没有货源链接，可能需要先添加货源链接');
                alert('请先添加货源商品链接，然后再执行自动填写');
                throw new Error('没有货源链接');
            }

            // 检查每行的链接状态
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td');

                if (cells.length >= 2) {
                    const linkCell = cells[1]; // 第2列是货源商品链接
                    const input = linkCell.querySelector('input');

                    if (input) {
                        const linkValue = input.value.trim();
                        console.log(`第 ${i + 1} 行链接: ${linkValue ? '已填写' : '空'}`);

                        if (!linkValue) {
                            console.log(`⚠️ 第 ${i + 1} 行货源链接为空`);
                        }
                    }
                }
            }
        } else {
            console.log('未找到货源链接表格');
        }
    }

    // 填写货源编号
    async fillSourceNumbers(modal) {
        console.log('开始填写货源编号...');

        // 方法1: 通过表格结构查找货源编号列
        const skuTable = modal.querySelector('.sku-table .ant-table-tbody');
        if (skuTable) {
            const rows = skuTable.querySelectorAll('tr:not(.ant-table-measure-row)');
            console.log(`在SKU表格中找到 ${rows.length} 行`);

            let filledCount = 0;

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                // 货源编号在第3列（索引为2）
                const cells = row.querySelectorAll('td');
                if (cells.length >= 3) {
                    const sourceNumberCell = cells[2]; // 第3列是货源编号
                    const select = sourceNumberCell.querySelector('.ant-select');

                    if (select) {
                        const selectedItem = select.querySelector('.ant-select-selection-item');
                        const placeholder = select.querySelector('.ant-select-selection-placeholder');

                        // 检查是否需要填写
                        const needsFill = (placeholder && placeholder.textContent.includes('请选择')) ||
                                         (selectedItem && selectedItem.textContent.includes('请选择')) ||
                                         !selectedItem;

                        if (needsFill) {
                            console.log(`填写第 ${i + 1} 行的货源编号`);

                            try {
                                // 点击下拉框
                                select.click();
                                await this.sleep(800);

                                // 查找下拉选项
                                const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                                if (dropdown) {
                                    const options = dropdown.querySelectorAll('.ant-select-item');
                                    console.log(`找到 ${options.length} 个选项`);

                                    let found = false;

                                    // 查找"编号1"选项
                                    for (let option of options) {
                                        const optionText = option.textContent.trim();
                                        console.log(`选项: ${optionText}`);

                                        if (optionText === '编号1' || optionText.includes('编号1')) {
                                            console.log('选择编号1');
                                            option.click();
                                            filledCount++;
                                            found = true;
                                            await this.sleep(800);
                                            break;
                                        }
                                    }

                                    // 如果没找到编号1，选择第一个可用选项
                                    if (!found && options.length > 0) {
                                        const firstOption = options[0];
                                        console.log(`选择第一个选项: ${firstOption.textContent.trim()}`);
                                        firstOption.click();
                                        filledCount++;
                                        await this.sleep(800);
                                    }
                                } else {
                                    console.log('未找到下拉选项，可能需要先添加货源链接');
                                    // 关闭下拉框
                                    modal.click();
                                    await this.sleep(300);
                                }
                            } catch (error) {
                                console.error(`填写第 ${i + 1} 行货源编号时出错:`, error);
                                modal.click();
                                await this.sleep(300);
                            }
                        } else {
                            console.log(`第 ${i + 1} 行货源编号已填写: ${selectedItem ? selectedItem.textContent : '未知'}`);
                        }
                    }
                }
            }

            console.log(`成功填写了 ${filledCount} 个货源编号`);

            if (filledCount === 0) {
                console.log('所有货源编号都已填写，或需要先添加货源链接');
            }

            // 等待系统处理
            await this.sleep(1500);
            return;
        }

        // 方法2: 如果没有找到表格，使用原来的方法
        console.log('未找到SKU表格，使用通用方法查找货源编号下拉框');

        const allSelects = modal.querySelectorAll('.ant-select');
        let filledCount = 0;

        for (let select of allSelects) {
            const placeholder = select.querySelector('.ant-select-selection-placeholder');
            const selectedItem = select.querySelector('.ant-select-selection-item');

            const isSourceNumberSelect = (placeholder && placeholder.textContent.includes('请选择编号')) ||
                                        (selectedItem && selectedItem.textContent.includes('请选择编号'));

            if (isSourceNumberSelect) {
                try {
                    select.click();
                    await this.sleep(500);

                    const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                    if (dropdown) {
                        const firstOption = dropdown.querySelector('.ant-select-item');
                        if (firstOption) {
                            firstOption.click();
                            filledCount++;
                            await this.sleep(500);
                        }
                    }
                } catch (error) {
                    console.error('填写货源编号出错:', error);
                    modal.click();
                    await this.sleep(300);
                }
            }
        }

        console.log(`通用方法填写了 ${filledCount} 个货源编号`);
    }

    // 等待SKU选项加载
    async waitForSKUOptions(modal) {
        console.log('等待SKU选项加载...');

        let attempts = 0;
        const maxAttempts = 30; // 增加等待时间

        while (attempts < maxAttempts) {
            // 查找SKU下拉框（通过多种方式定位）
            const skuSelects = modal.querySelectorAll('.ant-select');
            let availableOptions = 0;
            let totalSKUSelects = 0;

            for (let select of skuSelects) {
                // 检查是否是SKU选择框
                const placeholder = select.querySelector('.ant-select-selection-placeholder');
                const selectedItem = select.querySelector('.ant-select-selection-item');

                const isSKUSelect = (placeholder && placeholder.textContent.includes('请选择SKU')) ||
                                  (selectedItem && selectedItem.textContent.includes('请选择SKU')) ||
                                  select.style.width === '190px';

                if (isSKUSelect) {
                    totalSKUSelects++;

                    // 测试点击下拉框看是否有选项
                    try {
                        select.click();
                        await this.sleep(200);

                        const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
                        if (dropdown) {
                            const options = dropdown.querySelectorAll('.ant-select-item:not(.ant-select-item-option-disabled)');
                            if (options.length > 0) {
                                availableOptions++;
                                console.log(`SKU下拉框有 ${options.length} 个可用选项`);
                            }

                            // 关闭下拉框
                            modal.click();
                            await this.sleep(200);
                        }
                    } catch (error) {
                        console.log('测试SKU选项时出错:', error);
                        modal.click();
                        await this.sleep(200);
                    }
                }
            }

            console.log(`检查进度: ${availableOptions}/${totalSKUSelects} 个SKU下拉框有选项`);

            // 如果至少有一半的SKU下拉框有选项，认为加载完成
            if (totalSKUSelects > 0 && availableOptions >= Math.ceil(totalSKUSelects * 0.5)) {
                console.log('SKU选项加载完成');
                return;
            }

            // 如果没有找到SKU下拉框，可能还在加载中
            if (totalSKUSelects === 0) {
                console.log('未找到SKU下拉框，继续等待...');
            }

            await this.sleep(1000);
            attempts++;
        }

        throw new Error('SKU选项加载超时，请检查货源编号是否正确填写');
    }

    // 智能匹配SKU
    async matchSKUs(modal) {
        console.log('开始智能匹配SKU...');
        
        // 获取所有商品SKU行
        const skuRows = modal.querySelectorAll('.ant-table-tbody tr:not(.ant-table-measure-row)');
        const productSKUs = [];
        
        // 提取商品SKU信息
        for (let row of skuRows) {
            const skuCell = row.querySelector('td:first-child span[title]');
            if (skuCell) {
                const skuText = skuCell.getAttribute('title') || skuCell.textContent;
                productSKUs.push({
                    text: skuText,
                    row: row,
                    matched: false
                });
            }
        }
        
        // 按字数排序（字数多的优先）
        productSKUs.sort((a, b) => b.text.length - a.text.length);
        
        console.log('商品SKU列表:', productSKUs.map(item => item.text));
        
        // 为每个商品SKU匹配货源SKU
        for (let productSKU of productSKUs) {
            if (productSKU.matched) continue;
            
            await this.matchSingleSKU(productSKU, modal);
            await this.sleep(500); // 避免操作过快
        }
    }

    // 匹配单个SKU
    async matchSingleSKU(productSKU, modal) {
        console.log(`匹配SKU: ${productSKU.text}`);
        
        // 提取尺码信息
        const size = this.extractSize(productSKU.text);
        console.log(`提取的尺码: ${size}`);
        
        // 查找对应的货源SKU下拉框
        const skuSelect = productSKU.row.querySelector('.ant-select[style*="width: 190px"]');
        if (!skuSelect) return;
        
        // 点击下拉框
        skuSelect.click();
        await this.sleep(300);
        
        // 获取下拉选项
        const dropdown = document.querySelector('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
        if (!dropdown) return;
        
        const options = dropdown.querySelectorAll('.ant-select-item');
        const availableOptions = [];
        
        // 收集可用选项（排除已使用的）
        for (let option of options) {
            const optionText = option.textContent.trim();
            if (optionText &&
                !option.classList.contains('ant-select-item-option-disabled') &&
                !this.usedSourceSKUs.has(optionText)) {
                availableOptions.push({
                    element: option,
                    text: optionText
                });
            }
        }
        
        console.log('可用货源SKU选项:', availableOptions.map(opt => opt.text));
        
        // 智能匹配
        const bestMatch = this.findBestMatch(productSKU.text, size, availableOptions);
        
        if (bestMatch) {
            console.log(`匹配结果: ${productSKU.text} -> ${bestMatch.text}`);
            bestMatch.element.click();
            productSKU.matched = true;
            // 记录已使用的货源SKU
            this.usedSourceSKUs.add(bestMatch.text);
            await this.sleep(300);
        } else {
            console.log(`未找到匹配的货源SKU: ${productSKU.text}`);
            // 点击其他地方关闭下拉框
            modal.click();
            await this.sleep(300);
        }
    }

    // 提取尺码信息
    extractSize(skuText) {
        // 尺码匹配模式
        const sizePatterns = [
            /(\d+)cm/i,
            /(\d+)CM/i,
            /\b(\d+)\b/,
            /\b(S|M|L|XL|2XL|3XL)\b/i
        ];
        
        for (let pattern of sizePatterns) {
            const match = skuText.match(pattern);
            if (match) {
                return match[1].toUpperCase();
            }
        }
        
        return null;
    }

    // 查找最佳匹配
    findBestMatch(productSKU, productSize, availableOptions) {
        let bestMatch = null;
        let bestScore = 0;
        
        for (let option of availableOptions) {
            const score = this.calculateMatchScore(productSKU, productSize, option.text);
            if (score > bestScore) {
                bestScore = score;
                bestMatch = option;
            }
        }
        
        // 只有分数足够高才返回匹配结果
        return bestScore > 0.3 ? bestMatch : null;
    }

    // 计算匹配分数
    calculateMatchScore(productSKU, productSize, sourceSKU) {
        let score = 0;

        // 尺码必须完全匹配
        if (productSize) {
            const sourceSize = this.extractSize(sourceSKU);
            if (!sourceSize || sourceSize !== productSize) {
                return 0; // 尺码不匹配直接返回0
            }
            score += 0.4; // 尺码匹配加分
        }

        // 提取并清理关键词
        const productWords = this.extractKeywords(productSKU);
        const sourceWords = this.extractKeywords(sourceSKU);

        console.log(`商品关键词: [${productWords.join(', ')}]`);
        console.log(`货源关键词: [${sourceWords.join(', ')}]`);

        // 精确匹配分数
        let exactMatches = 0;
        let partialMatches = 0;

        for (let productWord of productWords) {
            let bestMatch = 0;

            for (let sourceWord of sourceWords) {
                if (productWord === sourceWord) {
                    // 完全匹配
                    bestMatch = Math.max(bestMatch, 1.0);
                } else if (productWord.length >= 2 && sourceWord.length >= 2) {
                    // 部分匹配
                    if (productWord.includes(sourceWord) || sourceWord.includes(productWord)) {
                        bestMatch = Math.max(bestMatch, 0.8);
                    } else {
                        // 计算相似度
                        const similarity = this.calculateSimilarity(productWord, sourceWord);
                        if (similarity > 0.6) {
                            bestMatch = Math.max(bestMatch, similarity * 0.6);
                        }
                    }
                }
            }

            if (bestMatch >= 1.0) {
                exactMatches++;
            } else if (bestMatch > 0) {
                partialMatches += bestMatch;
            }
        }

        // 计算关键词匹配分数
        if (productWords.length > 0) {
            const exactScore = (exactMatches / productWords.length) * 0.4;
            const partialScore = (partialMatches / productWords.length) * 0.2;
            score += exactScore + partialScore;
        }

        console.log(`匹配分数: ${score.toFixed(3)} (精确匹配: ${exactMatches}, 部分匹配: ${partialMatches.toFixed(2)})`);

        return score;
    }

    // 计算字符串相似度
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1.0;

        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    // 计算编辑距离
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    // 提取关键词
    extractKeywords(text) {
        // 移除尺码信息
        let cleaned = text.replace(/\d+cm/gi, '')
                         .replace(/\d+CM/gi, '')
                         .replace(/\b\d+\b/g, '')
                         .replace(/\b(S|M|L|XL|2XL|3XL)\b/gi, '');

        // 移除特殊字符和标点符号
        cleaned = cleaned.replace(/[;，,、+\-_\[\]【】（）()]/g, ' ')
                        .replace(/\s+/g, ' ')
                        .trim();

        // 分割并过滤关键词
        const words = cleaned.split(' ')
                            .filter(word => word.length >= 2)
                            .map(word => word.trim())
                            .filter(word => word.length > 0);

        // 去重
        return [...new Set(words)];
    }

    // 调试函数：分析页面结构
    debugPageStructure(modal) {
        console.log('=== 页面结构分析 ===');

        // 分析所有下拉框
        const allSelects = modal.querySelectorAll('.ant-select');
        console.log(`总共找到 ${allSelects.length} 个下拉框:`);

        allSelects.forEach((select, index) => {
            const placeholder = select.querySelector('.ant-select-selection-placeholder');
            const selectedItem = select.querySelector('.ant-select-selection-item');
            const style = select.getAttribute('style') || '';

            console.log(`下拉框 ${index + 1}:`);
            console.log(`  - 样式: ${style}`);
            console.log(`  - 占位符: ${placeholder ? placeholder.textContent : '无'}`);
            console.log(`  - 已选项: ${selectedItem ? selectedItem.textContent : '无'}`);
        });

        // 分析表格结构
        const tables = modal.querySelectorAll('.ant-table');
        console.log(`找到 ${tables.length} 个表格`);

        tables.forEach((table, index) => {
            const rows = table.querySelectorAll('.ant-table-tbody tr:not(.ant-table-measure-row)');
            console.log(`表格 ${index + 1} 有 ${rows.length} 行数据`);
        });
    }

    // 调试模式
    debugMode() {
        console.log('=== 进入调试模式 ===');

        const modal = document.querySelector('.ant-modal-content');
        if (!modal) {
            alert('请先打开关联货源弹窗');
            return;
        }

        // 分析页面结构
        this.debugPageStructure(modal);

        // 检查货源链接
        this.checkSourceLinks(modal).catch(console.error);

        alert('调试信息已输出到控制台，请按F12查看');
    }

    // 延迟函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化
const autoFillSKU = new AutoFillSKU();
