/* 浮动按钮样式 */
.auto-fill-sku-floating-btn {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    user-select: none;
}

.auto-fill-sku-floating-btn:hover {
    background-color: #45a049;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.auto-fill-sku-floating-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 运行状态样式 */
.auto-fill-sku-floating-btn.running {
    background-color: #ff6b6b;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 8px rgba(255, 107, 107, 0.6);
    }
    100% {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
}
