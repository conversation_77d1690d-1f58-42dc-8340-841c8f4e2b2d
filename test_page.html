<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>扩展功能测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section h3 {
            color: #333;
            border-bottom: 2px solid #87CEEB;
            padding-bottom: 10px;
        }
        
        .sku-example {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #87CEEB;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .match-arrow {
            text-align: center;
            font-size: 20px;
            color: #87CEEB;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .button {
            background: #87CEEB;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .button:hover {
            background: #5F9EA0;
        }
        
        .code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>名扬自动填写SKU - 扩展测试页面</h1>
        <p>此页面用于测试Chrome扩展的各项功能</p>
    </div>

    <div class="test-section">
        <h3>📋 安装检查清单</h3>
        <div id="install-checklist">
            <div class="status warning">
                <strong>检查中...</strong> 正在验证扩展安装状态
            </div>
        </div>
        <button class="button" onclick="checkInstallation()">重新检查安装</button>
    </div>

    <div class="test-section">
        <h3>🎯 智能匹配算法测试</h3>
        <p>以下是扩展的智能匹配示例：</p>
        
        <div class="sku-example">
            <strong>商品SKU:</strong> 花边外套 条纹裤;140cm
        </div>
        <div class="match-arrow">↓ 智能匹配 ↓</div>
        <div class="sku-example">
            <strong>货源SKU:</strong> 花边外套+条纹裤 140
        </div>
        
        <div class="sku-example">
            <strong>商品SKU:</strong> 单花边外套;90cm
        </div>
        <div class="match-arrow">↓ 智能匹配 ↓</div>
        <div class="sku-example">
            <strong>货源SKU:</strong> 花边外套 90
        </div>
        
        <h4>匹配规则说明：</h4>
        <ul>
            <li><strong>尺码匹配：</strong>支持 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, S, M, L, XL, 2XL 等</li>
            <li><strong>优先级：</strong>字数多的商品SKU优先匹配</li>
            <li><strong>避重复：</strong>已匹配的货源SKU不会重复使用</li>
            <li><strong>关键词：</strong>基于关键词相似度进行模糊匹配</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔧 功能测试</h3>
        <p>测试扩展的各项核心功能：</p>
        
        <button class="button" onclick="testKeywordExtraction()">测试关键词提取</button>
        <button class="button" onclick="testSizeExtraction()">测试尺码提取</button>
        <button class="button" onclick="testMatchingAlgorithm()">测试匹配算法</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h3>📖 使用说明</h3>
        <ol>
            <li>确保扩展已正确安装并启用</li>
            <li>打开目标网站：<code class="code">https://www.zzc168.com/#/orderCenter/order</code></li>
            <li>点击订单的"设置"按钮，打开"关联货源"弹窗</li>
            <li>点击页面右侧的蓝色"执行"按钮</li>
            <li>扩展将自动填写货源编号并匹配SKU</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🐛 故障排除</h3>
        <div class="status warning">
            <strong>常见问题：</strong>
            <ul>
                <li>执行按钮不出现 → 检查是否在正确的网站页面</li>
                <li>自动填写失败 → 确保"关联货源"弹窗已打开</li>
                <li>匹配不准确 → 检查商品SKU格式是否标准</li>
            </ul>
        </div>
        <button class="button" onclick="openConsole()">打开开发者控制台</button>
    </div>

    <script>
        function checkInstallation() {
            const checklist = document.getElementById('install-checklist');
            
            // 检查是否在Chrome浏览器中
            const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
            
            if (!isChrome) {
                checklist.innerHTML = `
                    <div class="status error">
                        <strong>❌ 浏览器检查失败</strong><br>
                        请使用Chrome浏览器运行此扩展
                    </div>
                `;
                return;
            }
            
            checklist.innerHTML = `
                <div class="status success">
                    <strong>✅ 浏览器检查通过</strong><br>
                    正在使用Chrome浏览器
                </div>
                <div class="status warning">
                    <strong>ℹ️ 扩展状态</strong><br>
                    请手动检查扩展是否已安装并启用<br>
                    在Chrome地址栏输入：<code class="code">chrome://extensions/</code>
                </div>
            `;
        }
        
        function testKeywordExtraction() {
            const testCases = [
                "花边外套 条纹裤;140cm",
                "单花边外套;90cm",
                "春秋季女童洋气时髦韩系公主慵懒风穿搭儿童宝宝秋装外套裤子套装"
            ];
            
            let results = '<h4>关键词提取测试结果：</h4>';
            
            testCases.forEach(testCase => {
                // 模拟关键词提取逻辑
                const keywords = extractKeywords(testCase);
                results += `
                    <div class="sku-example">
                        <strong>原文：</strong>${testCase}<br>
                        <strong>关键词：</strong>[${keywords.join(', ')}]
                    </div>
                `;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function testSizeExtraction() {
            const testCases = [
                "花边外套;140cm",
                "单花边外套;90CM", 
                "外套 120",
                "裤子 XL",
                "上衣 2XL"
            ];
            
            let results = '<h4>尺码提取测试结果：</h4>';
            
            testCases.forEach(testCase => {
                const size = extractSize(testCase);
                results += `
                    <div class="sku-example">
                        <strong>原文：</strong>${testCase}<br>
                        <strong>尺码：</strong>${size || '未识别'}
                    </div>
                `;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function testMatchingAlgorithm() {
            const testPairs = [
                {
                    product: "花边外套 条纹裤;140cm",
                    source: "花边外套+条纹裤 140"
                },
                {
                    product: "单花边外套;90cm", 
                    source: "花边外套 90"
                }
            ];
            
            let results = '<h4>匹配算法测试结果：</h4>';
            
            testPairs.forEach(pair => {
                const score = calculateMatchScore(pair.product, pair.source);
                const status = score > 0.5 ? 'success' : 'warning';
                
                results += `
                    <div class="status ${status}">
                        <strong>商品SKU：</strong>${pair.product}<br>
                        <strong>货源SKU：</strong>${pair.source}<br>
                        <strong>匹配分数：</strong>${score.toFixed(3)} ${score > 0.5 ? '✅' : '⚠️'}
                    </div>
                `;
            });
            
            document.getElementById('test-results').innerHTML = results;
        }
        
        function openConsole() {
            alert('请按 F12 或右键选择"检查"来打开开发者控制台，查看详细的调试信息。');
        }
        
        // 模拟扩展中的关键词提取函数
        function extractKeywords(text) {
            let cleaned = text.replace(/\d+cm/gi, '')
                             .replace(/\d+CM/gi, '')
                             .replace(/\b\d+\b/g, '')
                             .replace(/\b(S|M|L|XL|2XL|3XL)\b/gi, '');
            
            cleaned = cleaned.replace(/[;，,、+\-_\[\]【】（）()]/g, ' ')
                            .replace(/\s+/g, ' ')
                            .trim();
            
            const words = cleaned.split(' ')
                                .filter(word => word.length >= 2)
                                .map(word => word.trim())
                                .filter(word => word.length > 0);
            
            return [...new Set(words)];
        }
        
        // 模拟扩展中的尺码提取函数
        function extractSize(skuText) {
            const sizePatterns = [
                /(\d+)cm/i,
                /(\d+)CM/i,
                /\b(\d+)\b/,
                /\b(S|M|L|XL|2XL|3XL)\b/i
            ];
            
            for (let pattern of sizePatterns) {
                const match = skuText.match(pattern);
                if (match) {
                    return match[1].toUpperCase();
                }
            }
            
            return null;
        }
        
        // 模拟匹配分数计算
        function calculateMatchScore(productSKU, sourceSKU) {
            const productSize = extractSize(productSKU);
            const sourceSize = extractSize(sourceSKU);
            
            if (productSize && sourceSize && productSize !== sourceSize) {
                return 0;
            }
            
            const productWords = extractKeywords(productSKU);
            const sourceWords = extractKeywords(sourceSKU);
            
            let matchedWords = 0;
            for (let word of productWords) {
                if (sourceWords.some(sw => sw.includes(word) || word.includes(sw))) {
                    matchedWords++;
                }
            }
            
            let score = 0;
            if (productSize && sourceSize && productSize === sourceSize) {
                score += 0.4;
            }
            
            if (productWords.length > 0) {
                score += (matchedWords / productWords.length) * 0.6;
            }
            
            return score;
        }
        
        // 页面加载时自动检查安装
        window.onload = function() {
            checkInstallation();
        };
    </script>
</body>
</html>
