<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>生成图标</title>
</head>
<body>
    <canvas id="icon16" width="16" height="16"></canvas>
    <canvas id="icon48" width="48" height="48"></canvas>
    <canvas id="icon128" width="128" height="128"></canvas>
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建圆角矩形背景
            ctx.beginPath();
            const radius = size * 0.35; // 圆角半径
            ctx.moveTo(radius, 0);
            ctx.lineTo(size - radius, 0);
            ctx.quadraticCurveTo(size, 0, size, radius);
            ctx.lineTo(size, size - radius);
            ctx.quadraticCurveTo(size, size, size - radius, size);
            ctx.lineTo(radius, size);
            ctx.quadraticCurveTo(0, size, 0, size - radius);
            ctx.lineTo(0, radius);
            ctx.quadraticCurveTo(0, 0, radius, 0);
            ctx.closePath();
            
            // 填充深蓝色背景
            ctx.fillStyle = '#1e3a8a'; // 深蓝色
            ctx.fill();

            // 添加略深一点的蓝色边框，增加立体感
            ctx.strokeStyle = '#1e40af';
            ctx.lineWidth = size * 0.02;
            ctx.stroke();
            
            // 设置文字样式为白色
            ctx.fillStyle = '#FFFFFF';  // 白色
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 根据尺寸调整字体粗细和大小
            const fontSize = Math.floor(size * 0.75);
            if (size <= 16) {
                // 小尺寸图标使用稍微粗一点的字体
                ctx.font = `400 ${fontSize}px "Microsoft YaHei", sans-serif`;
            } else {
                // 大尺寸图标保持细体
                ctx.font = `300 ${fontSize}px "Microsoft YaHei Light", "YouYuan Light", "STYuan Light", sans-serif`;
            }
            
            // 绘制文字，稍微向上偏移以视觉居中
            ctx.fillText('辰', size/2, size/2 * 1.05);
            
            // 转换为PNG并下载
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 生成三种尺寸的图标
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
    </script>
</body>
</html> 