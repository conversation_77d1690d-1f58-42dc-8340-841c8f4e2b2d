# 名扬自动填写SKU Chrome扩展

## 功能介绍

这是一个专为 https://www.zzc168.com/#/orderCenter/order 网站设计的Chrome扩展，用于自动填写订单中心的SKU信息。

## 主要功能

1. **自动填写货源编号**: 自动将所有货源编号设置为"编号1"
2. **智能SKU匹配**: 基于尺码和关键词的智能匹配算法
3. **浮动操作按钮**: 页面右侧的蓝色圆形"执行"按钮
4. **实时状态显示**: 扩展弹窗显示当前页面状态

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装完成

## 使用方法

1. 打开 https://www.zzc168.com/#/orderCenter/order 页面
2. 点击任意订单的"设置"按钮，打开"关联货源"弹窗
3. **重要**: 确保已添加货源商品链接
4. 点击页面右侧的蓝色"执行"按钮
5. 扩展将自动执行以下操作：
   - 检查货源链接状态
   - 填写所有货源编号为"编号1"
   - 等待货源SKU选项加载
   - 智能匹配商品SKU与货源SKU

## 调试功能

- **左键点击**: 执行自动填写
- **右键点击**: 进入调试模式，输出详细信息到控制台

## 智能匹配规则

### 尺码匹配
- 支持的尺码格式：80, 90, 100, 110, 120, 130, 140, 150, 160, 170, S, M, L, XL, 2XL等
- 尺码必须完全一致才能匹配

### 匹配优先级
1. 按商品SKU字数排序（字数多的优先匹配）
2. 已匹配的货源SKU不会重复选择
3. 关键词模糊匹配

### 匹配示例
- 商品SKU: `花边外套 条纹裤;140cm` → 货源SKU: `花边外套+条纹裤 140`
- 商品SKU: `单花边外套;90cm` → 货源SKU: `花边外套 90`

## 文件结构

```
├── manifest.json          # 扩展配置文件
├── content.js             # 内容脚本（主要逻辑）
├── content.css            # 样式文件
├── background.js          # 背景脚本
├── popup.html             # 扩展弹窗页面
├── popup.js               # 弹窗脚本
├── create_icons.html      # 图标生成工具
├── icons/                 # 图标文件夹
└── README.md              # 说明文档
```

## 生成图标

1. 在浏览器中打开 `create_icons.html`
2. 页面会自动生成并下载三个尺寸的图标文件
3. 将下载的图标文件重命名并放入 `icons/` 文件夹：
   - `icon16.png` (16x16)
   - `icon48.png` (48x48)
   - `icon128.png` (128x128)

## 技术特点

- **智能等待**: 自动等待页面元素加载完成
- **错误处理**: 完善的错误处理和用户提示
- **非侵入式**: 不影响原网站功能
- **可视化反馈**: 实时显示操作状态

## 注意事项

1. 请确保在正确的页面使用此扩展
2. 使用前请先打开"关联货源"弹窗
3. 如遇到问题，请检查浏览器控制台的错误信息
4. 建议在使用前备份重要数据

## 版本信息

- 版本: 1.0
- 兼容性: Chrome 88+
- 目标网站: https://www.zzc168.com

## 更新日志

### v1.0 (2024-09-08)
- 初始版本发布
- 实现基本的自动填写功能
- 添加智能SKU匹配算法
- 创建用户友好的操作界面
