@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    名扬自动填写SKU Chrome扩展 - 快速安装
echo ==========================================
echo.

echo [1/3] 检查文件完整性...
if not exist "manifest.json" (
    echo ❌ 错误：manifest.json 文件不存在
    pause
    exit /b 1
)

if not exist "content.js" (
    echo ❌ 错误：content.js 文件不存在
    pause
    exit /b 1
)

if not exist "icons" (
    echo ⚠️  警告：icons 文件夹不存在，正在创建...
    mkdir icons
)

echo ✅ 基本文件检查完成

echo.
echo [2/3] 检查图标文件...
if not exist "icons\icon16.png" (
    echo ⚠️  警告：缺少 icon16.png
    echo    请运行 create_icons.html 生成图标文件
)

if not exist "icons\icon48.png" (
    echo ⚠️  警告：缺少 icon48.png
    echo    请运行 create_icons.html 生成图标文件
)

if not exist "icons\icon128.png" (
    echo ⚠️  警告：缺少 icon128.png
    echo    请运行 create_icons.html 生成图标文件
)

echo.
echo [3/3] 安装说明
echo.
echo 📋 请按以下步骤完成安装：
echo.
echo 1. 如果缺少图标文件，请先双击打开 create_icons.html
echo    页面会自动下载图标文件，将它们重命名为 .png 格式
echo    并放入 icons 文件夹中
echo.
echo 2. 打开 Chrome 浏览器
echo.
echo 3. 在地址栏输入：chrome://extensions/
echo.
echo 4. 开启右上角的"开发者模式"
echo.
echo 5. 点击"加载已解压的扩展程序"
echo.
echo 6. 选择当前文件夹：
echo    %CD%
echo.
echo 7. 扩展安装完成！
echo.
echo 🎯 使用方法：
echo    - 打开 https://www.zzc168.com/#/orderCenter/order
echo    - 点击订单设置，打开关联货源弹窗
echo    - 点击页面右侧的蓝色"执行"按钮
echo.
echo ==========================================
echo.

echo 是否要打开 Chrome 扩展管理页面？(Y/N)
set /p choice=请选择: 

if /i "%choice%"=="Y" (
    start chrome://extensions/
    echo.
    echo ✅ 已打开 Chrome 扩展管理页面
) else (
    echo.
    echo ℹ️  您可以稍后手动打开 chrome://extensions/
)

echo.
echo 是否要打开图标生成页面？(Y/N)
set /p choice2=请选择: 

if /i "%choice2%"=="Y" (
    start create_icons.html
    echo.
    echo ✅ 已打开图标生成页面
) else (
    echo.
    echo ℹ️  您可以稍后手动打开 create_icons.html
)

echo.
echo 安装向导完成！
echo 如有问题，请查看 安装指南.md 或 README.md
echo.
pause
