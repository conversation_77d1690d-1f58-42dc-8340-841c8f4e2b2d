<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>名扬自动填写SKU</title>
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: 500;
        }
        
        .status-value {
            color: #87CEEB;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #87CEEB;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
            font-size: 12px;
            line-height: 1.5;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
        
        .footer {
            text-align: center;
            font-size: 11px;
            opacity: 0.8;
            margin-top: 15px;
        }
        
        .version {
            color: #87CEEB;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>名扬自动填写SKU</h1>
    </div>
    
    <div class="status">
        <div class="status-item">
            <span class="status-label">当前页面:</span>
            <span class="status-value" id="current-page">检测中...</span>
        </div>
        <div class="status-item">
            <span class="status-label">扩展状态:</span>
            <span class="status-value" id="extension-status">就绪</span>
        </div>
    </div>
    
    <div class="instructions">
        <h3>使用说明:</h3>
        <ol>
            <li>打开 zzc168.com 订单中心页面</li>
            <li>点击订单的"设置"按钮打开关联货源弹窗</li>
            <li>点击页面右侧的蓝色"执行"按钮</li>
            <li>扩展将自动填写货源编号并智能匹配SKU</li>
        </ol>
    </div>
    
    <div class="footer">
        <div>版本 <span class="version">1.0</span></div>
        <div>© 2024 名扬自动填写SKU</div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
