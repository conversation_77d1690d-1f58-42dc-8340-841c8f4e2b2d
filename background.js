// 名扬自动填写SKU - 背景脚本

// 监听扩展安装
chrome.runtime.onInstalled.addListener(() => {
    console.log('名扬自动填写SKU扩展已安装');
});

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'log') {
        console.log('Content Script:', request.message);
    }
    
    if (request.action === 'notification') {
        // 可以在这里添加通知功能
        console.log('Notification:', request.message);
    }
    
    sendResponse({success: true});
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('zzc168.com')) {
        console.log('检测到目标网站页面加载完成');
    }
});
