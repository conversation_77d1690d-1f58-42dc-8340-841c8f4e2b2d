// 弹出页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 检查当前标签页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];
        const currentPageElement = document.getElementById('current-page');
        const extensionStatusElement = document.getElementById('extension-status');
        
        if (currentTab.url.includes('zzc168.com')) {
            if (currentTab.url.includes('#/orderCenter/order')) {
                currentPageElement.textContent = '订单中心 ✓';
                currentPageElement.style.color = '#90EE90';
                extensionStatusElement.textContent = '已激活';
                extensionStatusElement.style.color = '#90EE90';
            } else {
                currentPageElement.textContent = 'zzc168.com';
                currentPageElement.style.color = '#FFD700';
                extensionStatusElement.textContent = '请进入订单中心';
                extensionStatusElement.style.color = '#FFD700';
            }
        } else {
            currentPageElement.textContent = '非目标网站';
            currentPageElement.style.color = '#FF6B6B';
            extensionStatusElement.textContent = '未激活';
            extensionStatusElement.style.color = '#FF6B6B';
        }
    });
});
